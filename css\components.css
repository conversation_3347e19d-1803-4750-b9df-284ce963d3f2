/* Remove outlines from navigation elements */
.navbar *:focus,
.navbar *:active,
.navbar *:focus-visible {
    outline: none !important;
}

/* Navigation Styles */
.navbar {
    background: var(--navbar-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    padding: 1rem 0;
    width: 95%;
    min-width: 320px;
    max-width: 1400px;
    border-radius: 20px;
    border: 1px solid var(--navbar-border);
    transition: all 0.3s ease;
}

.navbar:hover {
    background: rgba(255, 255, 255, 0.6);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.5);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #333;
    transition: all 0.3s ease;
}

.mobile-menu-btn:hover {
    color: var(--primary-color);
    transform: scale(1.1);
}

.logo a {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
    display: block;
    padding: 5px;
    transition: all 0.3s ease;
}

.logo a:hover {
    transform: scale(1.05);
}

.logo svg {
    display: block;
    transition: all 0.3s ease;
}

.logo a:hover svg {
    filter: brightness(1.1) drop-shadow(0 0 8px rgba(75, 0, 130, 0.3));
}

.nav-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    margin-left: -80px;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-links a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 5px 0;
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-links a:hover::after,
.nav-links a.active::after {
    width: 100%;
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--primary-color);
    transform: translateY(-1px);
}

/* Dropdown Styles */
.dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 5px;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 1rem 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    display: block;
    padding: 0.5rem 1rem;
    color: #333;
    text-decoration: none;
    transition: background 0.3s ease;
}

.dropdown-menu a:hover {
    background: #f8f9fa;
    color: var(--primary-color);
}

/* Search Box */
.search-box {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 8px 16px;
    border: 1px solid #e1e5e9;
    transition: all 0.3s ease;
}

.search-box:hover {
    background: #fff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.search-box:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(75, 0, 130, 0.1);
    background: #fff;
}

.search-box input {
    border: none;
    background: none;
    outline: none;
    padding: 4px 8px;
    width: 180px;
    min-width: 120px;
    max-width: 250px;
    font-size: 0.9rem;
    color: #333;
    transition: all 0.3s ease;
}

.search-box input::placeholder {
    color: #999;
    transition: all 0.3s ease;
}

.search-box:hover input::placeholder {
    color: #666;
}

.search-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 4px;
    transition: all 0.3s ease;
}

.search-btn:hover {
    color: var(--primary-color);
    transform: scale(1.1);
}

/* Navigation Icons */
.nav-icons {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-icon {
    position: relative;
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-color);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    text-decoration: none;
}

.nav-icon:hover {
    background: var(--card-bg);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Theme Toggle Button */
.theme-toggle {
    position: relative;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 0;
}

.theme-toggle:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 12px rgba(75, 0, 130, 0.3);
}

.theme-toggle i {
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

/* Dark mode specific styling */
[data-theme="dark"] .theme-toggle {
    background: var(--card-bg);
    border-color: var(--border-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .theme-toggle:hover {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 4px 12px rgba(75, 0, 130, 0.5);
}

/* Focus styles for accessibility */
.theme-toggle:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

[data-theme="dark"] .theme-toggle:focus-visible {
    outline: 2px solid #8e24aa;
    outline-offset: 2px;
}

.badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid var(--background-color);
}

.nav-icon:hover .badge {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(75, 0, 130, 0.3);
}

/* Footer Styles */
.footer {
    background: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.footer-section p {
    margin-bottom: 1rem;
    opacity: 0.8;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: white;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.footer-section ul li a:hover {
    opacity: 1;
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #34495e;
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #34495e;
    padding-top: 1rem;
    text-align: center;
    opacity: 0.8;
}

/* Cart Modal Styles */
#cartModal .modal-content {
    max-width: 700px;
    width: 95%;
    min-width: 300px;
    max-height: 85vh;
    min-height: 300px;
}

#cartModal .modal-body {
    padding: 1rem 1.5rem;
    max-height: 50vh;
    overflow-y: auto;
}

.cart-item {
    display: flex;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.cart-item-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-info {
    flex: 1;
}

.cart-item-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.cart-item-price {
    color: var(--primary-color);
    font-weight: 600;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 4px;
}

.quantity {
    padding: 0.25rem 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 50px;
    text-align: center;
}

.remove-item {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    margin-left: auto;
}

.cart-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    background: var(--section-bg);
    border-radius: 0 0 15px 15px;
}

.cart-total {
    margin-bottom: 1rem;
    font-size: 1.2rem;
    color: var(--text-color);
}

.checkout-btn {
    width: 100%;
    padding: 12px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.checkout-btn:hover {
    background: #3a0066;
}

/* Dark mode cart styling */
[data-theme="dark"] .cart-item {
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .cart-item h4 {
    color: var(--text-color);
}

[data-theme="dark"] .cart-item p {
    color: var(--text-light);
}

[data-theme="dark"] .quantity-btn {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .quantity-btn:hover {
    background: var(--primary-color);
    color: white;
}

[data-theme="dark"] .quantity {
    background: var(--input-bg);
    border: 1px solid var(--input-border);
    color: var(--text-color);
}

[data-theme="dark"] .remove-item {
    color: var(--primary-color);
}

[data-theme="dark"] .remove-item:hover {
    color: #ff4757;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--modal-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1002;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.open {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--modal-bg);
    border-radius: 15px;
    max-width: 600px;
    width: 90%;
    min-width: 280px;
    max-height: 85vh;
    min-height: 200px;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
    border: 1px solid var(--border-color);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal.open .modal-content {
    transform: scale(1);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--modal-bg);
}

.modal-header h3 {
    color: var(--text-color);
    margin: 0;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: var(--text-color);
}

.modal-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
    background: var(--modal-bg);
    color: var(--text-color);
}

/* Overlay */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--modal-overlay);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Modern Scrollbar Styles */
::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

::-webkit-scrollbar-track {
    background: rgba(75, 0, 130, 0.05);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 10px;
    border: 3px solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    border: 2px solid rgba(255, 255, 255, 0.8);
}

/* Firefox Scrollbar */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) rgba(75, 0, 130, 0.05);
}

/* Update scroll effect in main.js */
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 100) {
        navbar.style.boxShadow = '0 4px 30px rgba(0, 0, 0, 0.15)';
    } else {
        navbar.style.boxShadow = '0 4px 30px rgba(0, 0, 0, 0.1)';
    }
});

/* Container and Section Spacing */
.container {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
}

section {
    padding: 6rem 0;
    margin-top: 2rem;
    position: relative;
    width: 100%;
}

/* Hero Section Spacing */
.hero {
    min-height: 100vh;
    height: auto;
    margin-top: 0;
    padding: 0;
    display: flex;
    align-items: center;
    position: relative;
    width: 100%;
}

/* Products Section */
.products-section {
    padding: 6rem 0;
    margin-top: 2rem;
}

/* Categories Section */
.categories-section {
    padding: 6rem 0;
    margin-top: 2rem;
}

/* Newsletter Section */
.newsletter-section {
    padding: 6rem 0;
    margin-top: 2rem;
}

/* Footer Spacing */
.footer {
    margin-top: 4rem;
    padding: 4rem 0 2rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    section {
        padding: 4rem 0;
        margin-top: 1rem;
    }

    .hero {
        min-height: 100vh;
        margin-top: 0;
        padding: 0;
    }

    .products-section,
    .categories-section,
    .newsletter-section {
        padding: 4rem 0;
        margin-top: 1rem;
    }
}

@media (max-width: 480px) {
    section {
        padding: 3rem 0;
        margin-top: 0.5rem;
    }

    .hero {
        min-height: 100vh;
        margin-top: 0;
        padding: 0;
    }

    .products-section,
    .categories-section,
    .newsletter-section {
        padding: 3rem 0;
        margin-top: 0.5rem;
    }
}
