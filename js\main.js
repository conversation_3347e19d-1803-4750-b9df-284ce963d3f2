// Main JavaScript functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initializeNavigation();
    initializeProductGrid();
    initializeSearch();
    initializeNewsletter();
    initializeHeroSection();

    updateCartCount();
    updateFavoritesCount();
});

// Sample product data
const products = [
    {
        id: 1,
        title: "Summer Floral Dress",
        price: 29.99,
        originalPrice: 49.99,
        image: "https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400&h=500&fit=crop",
        rating: 4.5,
        reviews: 128,
        category: "women",
        onSale: true
    },
    {
        id: 2,
        title: "Classic White Shirt",
        price: 24.99,
        originalPrice: 34.99,
        image: "https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=400&h=500&fit=crop",
        rating: 4.2,
        reviews: 89,
        category: "men",
        onSale: true
    },
    {
        id: 3,
        title: "Denim Jacket",
        price: 59.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=500&fit=crop",
        rating: 4.7,
        reviews: 203,
        category: "women",
        onSale: false
    },
    {
        id: 4,
        title: "Casual Sneakers",
        price: 79.99,
        originalPrice: 99.99,
        image: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=500&fit=crop",
        rating: 4.4,
        reviews: 156,
        category: "shoes",
        onSale: true
    },
    {
        id: 5,
        title: "Bohemian Maxi Dress",
        price: 45.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1572804013309-59a88b7e92f1?w=400&h=500&fit=crop",
        rating: 4.6,
        reviews: 92,
        category: "women",
        onSale: false
    },
    {
        id: 6,
        title: "Leather Handbag",
        price: 89.99,
        originalPrice: 129.99,
        image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=500&fit=crop",
        rating: 4.8,
        reviews: 74,
        category: "accessories",
        onSale: true
    }
];

// Navigation functionality
function initializeNavigation() {
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const navCenter = document.querySelector('.nav-center');
    const overlay = document.getElementById('overlay');

    // Mobile menu toggle
    mobileMenuBtn?.addEventListener('click', function() {
        navCenter.classList.toggle('active');
        overlay.classList.toggle('active');
        document.body.style.overflow = navCenter.classList.contains('active') ? 'hidden' : '';
    });

    // Close mobile menu when clicking overlay
    overlay?.addEventListener('click', function() {
        navCenter.classList.remove('active');
        overlay.classList.remove('active');
        document.body.style.overflow = '';
        document.getElementById('cartModal')?.classList.remove('open');
        document.getElementById('favoritesModal')?.classList.remove('open');
    });

    // Close mobile menu when clicking a nav link
    const navLinks = document.querySelectorAll('.nav-links a');
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            navCenter.classList.remove('active');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        });
    });

    // Dropdown functionality for desktop
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
        const toggle = dropdown.querySelector('.dropdown-toggle');
        const menu = dropdown.querySelector('.dropdown-menu');

        if (window.innerWidth > 768) {
            dropdown.addEventListener('mouseenter', () => {
                menu.style.opacity = '1';
                menu.style.visibility = 'visible';
                menu.style.transform = 'translateY(0)';
            });

            dropdown.addEventListener('mouseleave', () => {
                menu.style.opacity = '0';
                menu.style.visibility = 'hidden';
                menu.style.transform = 'translateY(-10px)';
            });
        }
    });

    // Navbar scroll effect - will be replaced by theme-aware handler
    // This is handled by updateNavbarForTheme function
}

// Product grid functionality
function initializeProductGrid() {
    const featuredContainer = document.getElementById('featuredProducts');
    const trendingContainer = document.getElementById('trendingProducts');

    if (featuredContainer) {
        renderProducts(products.slice(0, 4), featuredContainer);
    }

    if (trendingContainer) {
        renderProducts(products.slice(2, 6), trendingContainer);
    }
}

// Render products function
function renderProducts(productList, container) {
    container.innerHTML = '';
    
    productList.forEach(product => {
        const productCard = createProductCard(product);
        container.appendChild(productCard);
    });
}

// Create product card
function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';
    card.setAttribute('data-product-id', product.id);

    const discount = product.originalPrice ? 
        Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100) : 0;

    card.innerHTML = `
        <div class="product-image">
            <img src="${product.image}" alt="${product.title}" loading="lazy">
            <div class="product-actions">
                <button class="action-btn favorite-btn" data-product-id="${product.id}" title="Add to Favorites">
                    <i class="far fa-heart"></i>
                </button>
                <button class="action-btn quick-view-btn" data-product-id="${product.id}" title="Quick View">
                    <i class="fas fa-eye"></i>
                </button>
            </div>
            ${product.onSale && discount > 0 ? `<div class="discount-badge">-${discount}%</div>` : ''}
        </div>
        <div class="product-info">
            <h3 class="product-title">${product.title}</h3>
            <div class="product-price">
                <span class="current-price">$${product.price}</span>
                ${product.originalPrice ? `<span class="original-price">$${product.originalPrice}</span>` : ''}
            </div>
            <div class="product-rating">
                <div class="stars">
                    ${generateStars(product.rating)}
                </div>
                <span class="rating-count">(${product.reviews})</span>
            </div>
            <div class="product-card-actions">
                <button class="btn btn-primary add-to-cart-btn" data-product-id="${product.id}">
                    <i class="fas fa-shopping-cart"></i>
                    Add to Cart
                </button>
                <button class="btn btn-outline favorite-btn-text" data-product-id="${product.id}">
                    <i class="far fa-heart"></i>
                    Add to Favorites
                </button>
            </div>
        </div>
    `;

    // Add event listeners
    const favoriteBtn = card.querySelector('.favorite-btn');
    const favoriteBtnText = card.querySelector('.favorite-btn-text');
    const addToCartBtn = card.querySelector('.add-to-cart-btn');
    const quickViewBtn = card.querySelector('.quick-view-btn');

    // Favorite button (icon) event listener
    favoriteBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        toggleFavorite(product.id, product.title);
    });

    // Favorite button (text) event listener
    favoriteBtnText.addEventListener('click', (e) => {
        e.stopPropagation();
        toggleFavorite(product.id, product.title);
    });

    // Add to cart button event listener
    addToCartBtn.addEventListener('click', (e) => {
        e.stopPropagation();

        // Add visual feedback
        addToCartBtn.style.transform = 'scale(0.95)';
        setTimeout(() => {
            addToCartBtn.style.transform = '';
        }, 150);

        addToCart(product);
    });

    // Quick view button event listener
    quickViewBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        // For now, navigate to product page instead of modal
        window.location.href = `product.html?id=${product.id}`;
    });

    // Navigate to product page on card click (but not on button clicks)
    card.addEventListener('click', (e) => {
        // Only navigate if the click wasn't on a button
        if (!e.target.closest('button')) {
            window.location.href = `product.html?id=${product.id}`;
        }
    });

    return card;
}

// Generate star rating
function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let stars = '';

    for (let i = 0; i < fullStars; i++) {
        stars += '<i class="fas fa-star"></i>';
    }

    if (hasHalfStar) {
        stars += '<i class="fas fa-star-half-alt"></i>';
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
        stars += '<i class="far fa-star"></i>';
    }

    return stars;
}

// Search functionality
function initializeSearch() {
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.querySelector('.search-btn');

    function performSearch() {
        const query = searchInput.value.trim();
        if (query) {
            // In a real app, this would make an API call
            console.log('Searching for:', query);
            // Redirect to search results page
            window.location.href = `search.html?q=${encodeURIComponent(query)}`;
        }
    }

    searchBtn?.addEventListener('click', performSearch);
    searchInput?.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
}

// Newsletter functionality
function initializeNewsletter() {
    const newsletterForm = document.getElementById('newsletterForm');
    
    newsletterForm?.addEventListener('submit', function(e) {
        e.preventDefault();
        const email = this.querySelector('input[type="email"]').value;
        
        // Simulate API call
        setTimeout(() => {
            alert('Thank you for subscribing!');
            this.reset();
        }, 500);
    });
}

// Utility functions
function updateCartCount() {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
        cartCount.textContent = cart.reduce((total, item) => total + item.quantity, 0);
    }
}

function updateFavoritesCount() {
    const favorites = JSON.parse(localStorage.getItem('favorites')) || [];
    const favoritesCount = document.getElementById('favoritesCount');
    if (favoritesCount) {
        favoritesCount.textContent = favorites.length;
    }
}

// Add to cart function
function addToCart(product) {
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    const existingItem = cart.find(item => item.id === product.id);

    if (existingItem) {
        existingItem.quantity += 1;
        showNotification(`${product.title} quantity updated in cart!`);
    } else {
        cart.push({
            id: product.id,
            title: product.title,
            price: product.price,
            image: product.image,
            quantity: 1
        });
        showNotification(`${product.title} added to cart!`);
    }

    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();
}

// Toggle favorite function
function toggleFavorite(productId, productTitle = 'Product') {
    let favorites = JSON.parse(localStorage.getItem('favorites')) || [];
    const index = favorites.indexOf(productId);

    if (index > -1) {
        favorites.splice(index, 1);
        showNotification(`${productTitle} removed from favorites`);
    } else {
        favorites.push(productId);
        showNotification(`${productTitle} added to favorites!`);
    }

    localStorage.setItem('favorites', JSON.stringify(favorites));
    updateFavoritesCount();
    updateFavoriteButtons();
}

// Update favorite button states
function updateFavoriteButtons() {
    const favorites = JSON.parse(localStorage.getItem('favorites')) || [];

    // Update icon favorite buttons
    const favoriteButtons = document.querySelectorAll('.favorite-btn');
    favoriteButtons.forEach(btn => {
        const productId = parseInt(btn.getAttribute('data-product-id'));
        const icon = btn.querySelector('i');

        if (favorites.includes(productId)) {
            icon.className = 'fas fa-heart';
            btn.style.color = '#4B0082';
        } else {
            icon.className = 'far fa-heart';
            btn.style.color = '';
        }
    });

    // Update text favorite buttons
    const favoriteTextButtons = document.querySelectorAll('.favorite-btn-text');
    favoriteTextButtons.forEach(btn => {
        const productId = parseInt(btn.getAttribute('data-product-id'));
        const icon = btn.querySelector('i');

        if (favorites.includes(productId)) {
            icon.className = 'fas fa-heart';
            btn.classList.add('favorited');
            btn.innerHTML = '<i class="fas fa-heart"></i> Remove from Favorites';
            btn.style.background = '#4B0082';
            btn.style.color = 'white';
            btn.style.borderColor = '#4B0082';
        } else {
            icon.className = 'far fa-heart';
            btn.classList.remove('favorited');
            btn.innerHTML = '<i class="far fa-heart"></i> Add to Favorites';
            btn.style.background = '';
            btn.style.color = '';
            btn.style.borderColor = '';
        }
    });
}

// Show notification
function showNotification(message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: #4B0082;
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        z-index: 1003;
        animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Add CSS for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize favorite button states on page load
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(updateFavoriteButtons, 100);
});

// Theme Toggle Functionality
function initializeThemeToggle() {
    console.log('Initializing theme toggle...');

    const themeToggle = document.getElementById('themeToggle');
    const themeIcon = document.getElementById('themeIcon');

    console.log('Theme toggle elements:', { themeToggle, themeIcon });

    if (!themeToggle || !themeIcon) {
        console.warn('Theme toggle elements not found');
        return;
    }

    // Load saved theme or default to light
    const savedTheme = localStorage.getItem('theme') || 'light';
    console.log('Saved theme:', savedTheme);
    applyTheme(savedTheme);

    // Theme toggle click handler
    themeToggle.addEventListener('click', function(e) {
        e.preventDefault();
        console.log('Theme toggle clicked!');

        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';

        console.log('Switching from', currentTheme, 'to', newTheme);

        applyTheme(newTheme);
        localStorage.setItem('theme', newTheme);

        // Add visual feedback
        themeToggle.style.transform = 'scale(0.9)';
        setTimeout(() => {
            themeToggle.style.transform = '';
        }, 150);
    });

    console.log('Theme toggle initialized successfully');
}

function applyTheme(theme) {
    console.log('Applying theme:', theme);

    const themeIcon = document.getElementById('themeIcon');
    const themeToggle = document.getElementById('themeToggle');

    // Apply theme to document
    document.documentElement.setAttribute('data-theme', theme);

    if (theme === 'dark') {
        document.body.classList.add('dark-theme');
        if (themeIcon) {
            themeIcon.className = 'fas fa-sun';
        }
        if (themeToggle) {
            themeToggle.setAttribute('title', 'Switch to light mode');
            themeToggle.setAttribute('aria-label', 'Switch to light mode');
        }
        console.log('Dark theme applied');
    } else {
        document.body.classList.remove('dark-theme');
        if (themeIcon) {
            themeIcon.className = 'fas fa-moon';
        }
        if (themeToggle) {
            themeToggle.setAttribute('title', 'Switch to dark mode');
            themeToggle.setAttribute('aria-label', 'Switch to dark mode');
        }
        console.log('Light theme applied');
    }

    console.log('Current data-theme:', document.documentElement.getAttribute('data-theme'));
    console.log('Body classes:', document.body.className);
}

// Initialize theme immediately when script loads
(function() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    console.log('Early theme initialization:', savedTheme);

    document.documentElement.setAttribute('data-theme', savedTheme);
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
    }
})();
