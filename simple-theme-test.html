<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Theme Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --background-color: #ffffff;
            --text-color: #333;
            --card-bg: #ffffff;
            --border-color: #e1e5e9;
            --primary-color: #4B0082;
        }

        [data-theme="dark"],
        body.dark-theme {
            --background-color: #0f0f0f;
            --text-color: #e4e4e7;
            --card-bg: #18181b;
            --border-color: #27272a;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            transition: all 0.3s ease;
            margin: 0;
            padding: 20px;
        }

        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--card-bg);
            border: 2px solid var(--border-color);
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            z-index: 1000;
        }

        .theme-toggle:hover {
            background: var(--primary-color);
            color: white;
            transform: scale(1.1);
        }

        .test-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            transition: all 0.3s ease;
        }

        .test-card h2 {
            color: var(--text-color);
            margin-top: 0;
        }

        .test-card p {
            color: var(--text-color);
            opacity: 0.8;
        }

        .status {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            padding: 10px 20px;
            border-radius: 5px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <button class="theme-toggle" id="themeToggle" title="Toggle theme">
        <i class="fas fa-moon" id="themeIcon"></i>
    </button>

    <h1>Simple Theme Test</h1>
    
    <div class="test-card">
        <h2>Test Card 1</h2>
        <p>This card should change colors when you click the theme toggle button.</p>
    </div>

    <div class="test-card">
        <h2>Test Card 2</h2>
        <p>Background should be dark in dark mode and light in light mode.</p>
    </div>

    <div class="status" id="status">
        Theme: <span id="currentTheme">light</span>
    </div>

    <script>
        console.log('Script starting...');
        
        // Initialize theme immediately
        const savedTheme = localStorage.getItem('theme') || 'light';
        console.log('Saved theme:', savedTheme);
        applyTheme(savedTheme);

        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM ready');
            
            const themeToggle = document.getElementById('themeToggle');
            const themeIcon = document.getElementById('themeIcon');
            const currentThemeSpan = document.getElementById('currentTheme');
            
            console.log('Elements found:', { themeToggle, themeIcon, currentThemeSpan });
            
            if (!themeToggle || !themeIcon) {
                console.error('Theme toggle elements not found!');
                return;
            }

            // Add click handler
            themeToggle.addEventListener('click', function() {
                console.log('Theme toggle clicked!');
                
                const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
                const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                
                console.log('Switching from', currentTheme, 'to', newTheme);
                
                applyTheme(newTheme);
                localStorage.setItem('theme', newTheme);
                
                // Update status
                if (currentThemeSpan) {
                    currentThemeSpan.textContent = newTheme;
                }
                
                // Visual feedback
                themeToggle.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    themeToggle.style.transform = '';
                }, 150);
            });
            
            // Update initial status
            if (currentThemeSpan) {
                currentThemeSpan.textContent = savedTheme;
            }
        });

        function applyTheme(theme) {
            console.log('Applying theme:', theme);
            
            const themeIcon = document.getElementById('themeIcon');
            
            if (theme === 'dark') {
                document.documentElement.setAttribute('data-theme', 'dark');
                document.body.classList.add('dark-theme');
                if (themeIcon) {
                    themeIcon.className = 'fas fa-sun';
                }
                console.log('Dark theme applied');
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                document.body.classList.remove('dark-theme');
                if (themeIcon) {
                    themeIcon.className = 'fas fa-moon';
                }
                console.log('Light theme applied');
            }
            
            // Log current state
            console.log('Current data-theme:', document.documentElement.getAttribute('data-theme'));
            console.log('Body classes:', document.body.className);
        }
    </script>
</body>
</html>
