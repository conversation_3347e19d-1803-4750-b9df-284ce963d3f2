<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dark Mode Test - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-left">
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html">
                        <svg width="90" height="40" viewBox="0 0 90 40" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#4B0082;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#D8BFD8;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <text x="5" y="28" font-family="Inter, sans-serif" font-size="24" font-weight="700" fill="url(#logoGradient)">VAITH</text>
                        </svg>
                    </a>
                </div>
            </div>
            
            <div class="nav-center">
                <ul class="nav-links" id="navLinks">
                    <li><a href="index.html" class="active">Home</a></li>
                    <li><a href="products.html">Products</a></li>
                    <li><a href="sale.html">Sale</a></li>
                </ul>
                <div class="search-box">
                    <input type="text" placeholder="Search for products..." id="searchInput">
                    <button class="search-btn"><i class="fas fa-search"></i></button>
                </div>
            </div>

            <div class="nav-right">
                <div class="nav-icons">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <a href="login.html" class="nav-icon">
                        <i class="fas fa-user"></i>
                    </a>
                    <button class="nav-icon" id="favoritesBtn">
                        <i class="fas fa-heart"></i>
                        <span class="badge" id="favoritesCount">0</span>
                    </button>
                    <button class="nav-icon" id="cartBtn">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="badge" id="cartCount">0</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Test Content -->
    <div style="margin-top: 120px; padding: 2rem;">
        <div class="container">
            <h1 class="section-title">Dark Mode Test Page</h1>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin: 2rem 0;">
                <!-- Test Card 1 -->
                <div style="background: var(--card-bg); border: 1px solid var(--border-color); border-radius: 10px; padding: 1.5rem; transition: all 0.3s ease;">
                    <h3 style="color: var(--text-color); margin-bottom: 1rem;">Test Card</h3>
                    <p style="color: var(--text-light); margin-bottom: 1rem;">This is a test card to verify dark mode styling works correctly.</p>
                    <button class="btn btn-primary">Test Button</button>
                </div>
                
                <!-- Test Form -->
                <div style="background: var(--card-bg); border: 1px solid var(--border-color); border-radius: 10px; padding: 1.5rem;">
                    <h3 style="color: var(--text-color); margin-bottom: 1rem;">Test Form</h3>
                    <input type="text" placeholder="Test input" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; background: var(--input-bg); border: 1px solid var(--input-border); color: var(--text-color); border-radius: 4px;">
                    <textarea placeholder="Test textarea" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; background: var(--input-bg); border: 1px solid var(--input-border); color: var(--text-color); border-radius: 4px; resize: vertical; height: 80px;"></textarea>
                    <button class="btn btn-primary">Submit</button>
                </div>
            </div>
            
            <!-- Instructions -->
            <div style="background: var(--card-bg); border: 1px solid var(--border-color); border-radius: 10px; padding: 2rem; margin: 2rem 0;">
                <h2 style="color: var(--text-color); margin-bottom: 1rem;">Dark Mode Test Instructions</h2>
                <ol style="color: var(--text-color); line-height: 1.6;">
                    <li>Click the moon/sun icon in the navigation bar to toggle between light and dark modes</li>
                    <li>Verify that all elements change colors appropriately</li>
                    <li>Check that the theme preference is saved when you refresh the page</li>
                    <li>Test the modal by clicking the cart or favorites icons</li>
                    <li>Verify that form elements have proper contrast in both modes</li>
                </ol>
            </div>
        </div>
    </div>

    <!-- Test Modal -->
    <div class="modal" id="testModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Test Modal</h3>
                <button class="close-modal" id="closeTestModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p>This is a test modal to verify dark mode styling works for modals.</p>
                <button class="btn btn-primary" style="margin-top: 1rem;">Test Button in Modal</button>
            </div>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay"></div>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script>
        // Add test modal functionality
        document.addEventListener('DOMContentLoaded', function() {
            const cartBtn = document.getElementById('cartBtn');
            const testModal = document.getElementById('testModal');
            const closeTestModal = document.getElementById('closeTestModal');
            const overlay = document.getElementById('overlay');
            
            cartBtn?.addEventListener('click', function() {
                testModal.classList.add('open');
                overlay.classList.add('active');
            });
            
            closeTestModal?.addEventListener('click', function() {
                testModal.classList.remove('open');
                overlay.classList.remove('active');
            });
            
            overlay?.addEventListener('click', function() {
                testModal.classList.remove('open');
                overlay.classList.remove('active');
            });
        });
    </script>
</body>
</html>
