/* Reset and Base Styles */
:root {
    /* Light Theme Colors */
    --primary-color: #4B0082;      /* Dark Purple */
    --secondary-color: #D8BFD8;    /* Light Purple (Lavender) */
    --background-color: #FFFFFF;   /* White */
    --text-color: #333;
    --text-light: #666;
    --border-color: #e1e5e9;
    --success-color: #27ae60;
    --warning-color: #ffc107;
    --error-color: #e74c3c;

    /* Theme-aware colors */
    --card-bg: #ffffff;
    --card-border: #e1e5e9;
    --input-bg: #ffffff;
    --input-border: #e1e5e9;
    --modal-bg: #ffffff;
    --modal-overlay: rgba(0, 0, 0, 0.5);
    --navbar-bg: rgba(255, 255, 255, 0.95);
    --navbar-border: rgba(255, 255, 255, 0.3);
    --section-bg: #f8f9fa;
    --footer-bg: #2c3e50;
    --footer-text: #ecf0f1;

    /* Modern Hero Variables */
    --hero-gradient-1: #667eea;
    --hero-gradient-2: #764ba2;
    --hero-gradient-3: #f093fb;
    --hero-text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    --hero-glass-bg: rgba(255, 255, 255, 0.1);
    --hero-glass-border: rgba(255, 255, 255, 0.3);
    --hero-animation-duration: 15s;
    --hero-transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    /* Spacing Variables */
    --hero-padding-mobile: 2rem 0;
    --hero-padding-tablet: 4rem 0;
    --hero-padding-desktop: 6rem 0;
    --hero-gap-mobile: 2rem;
    --hero-gap-tablet: 4rem;
    --hero-gap-desktop: 6rem;
}

/* Dark Theme Colors */
[data-theme="dark"],
body.dark-theme {
    --background-color: #0f0f0f;
    --text-color: #e4e4e7;
    --text-light: #a1a1aa;
    --border-color: #27272a;
    --card-bg: #18181b;
    --card-border: #27272a;
    --input-bg: #18181b;
    --input-border: #3f3f46;
    --modal-bg: #18181b;
    --modal-overlay: rgba(0, 0, 0, 0.8);
    --navbar-bg: rgba(24, 24, 27, 0.95);
    --navbar-border: rgba(63, 63, 70, 0.3);
    --section-bg: #09090b;
    --footer-bg: #18181b;
    --footer-text: #e4e4e7;

    /* Adjust hero glass effects for dark mode */
    --hero-glass-bg: rgba(0, 0, 0, 0.2);
    --hero-glass-border: rgba(255, 255, 255, 0.1);
}

/* Dark mode specific component styling */
[data-theme="dark"] .hero-badge,
body.dark-theme .hero-badge {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .cta-btn.secondary,
body.dark-theme .cta-btn.secondary {
    background: rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .cta-btn.secondary:hover,
body.dark-theme .cta-btn.secondary:hover {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
}

[data-theme="dark"] .category-card,
body.dark-theme .category-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .category-overlay,
body.dark-theme .category-overlay {
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.3));
}

[data-theme="dark"] .section-title,
body.dark-theme .section-title {
    color: var(--text-color);
}

/* Dark mode newsletter styling */
[data-theme="dark"] .newsletter-form input,
body.dark-theme .newsletter-form input {
    background: var(--input-bg);
    color: var(--text-color);
    border: 1px solid var(--input-border);
}

[data-theme="dark"] .newsletter-form input::placeholder,
body.dark-theme .newsletter-form input::placeholder {
    color: var(--text-light);
}

[data-theme="dark"] .newsletter-form input:focus,
body.dark-theme .newsletter-form input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(75, 0, 130, 0.2);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Remove outlines from all interactive elements */
*:focus,
*:active,
*:focus-visible {
    outline: none !important;
}

/* Remove outlines from buttons */
button:focus,
button:active,
button:focus-visible {
    outline: none !important;
}

/* Remove outlines from links */
a:focus,
a:active,
a:focus-visible {
    outline: none !important;
}

/* Remove outlines from inputs */
input:focus,
input:active,
input:focus-visible,
textarea:focus,
textarea:active,
textarea:focus-visible,
select:focus,
select:active,
select:focus-visible {
    outline: none !important;
}

/* Remove outlines from other interactive elements */
div:focus,
div:active,
span:focus,
span:active {
    outline: none !important;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    width: 100%;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.2rem;
    font-weight: 700;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #3a0066;
    transform: translateY(-2px);
}

/* Modern CTA Button with Advanced Effects */
.cta-btn {
    background: linear-gradient(135deg,
        var(--primary-color) 0%,
        #6a1b9a 50%,
        #8e24aa 100%);
    color: white;
    padding: 18px 40px;
    border: none;
    border-radius: 50px;
    font-size: 1.2rem;
    font-weight: 700;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow:
        0 8px 25px rgba(75, 0, 130, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Glassmorphism effect for button */
.cta-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent);
    transition: left 0.6s ease;
}

.cta-btn:hover::before {
    left: 100%;
}

.cta-btn:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow:
        0 15px 35px rgba(75, 0, 130, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg,
        #6a1b9a 0%,
        var(--primary-color) 50%,
        #9c27b0 100%);
}

.cta-btn:active {
    transform: translateY(-2px) scale(1.02);
    transition: all 0.1s ease;
}

/* Hero Section - Super Modern Design */
.hero {
    background: linear-gradient(135deg,
        #667eea 0%,
        #764ba2 25%,
        var(--primary-color) 50%,
        #f093fb 75%,
        var(--secondary-color) 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
    height: auto;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    margin-top: 0;
    padding: 0;
    width: 100%;
}

/* Page Hero (for other pages) */
.page-hero {
    background: linear-gradient(135deg,
        #667eea 0%,
        #764ba2 25%,
        var(--primary-color) 50%,
        #f093fb 75%,
        var(--secondary-color) 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 50vh;
    height: auto;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    margin-top: 0;
    padding: 0;
    width: 100%;
}

.page-hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    padding: 2rem;
    width: 100%;
}

.page-hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: var(--hero-text-shadow);
}

.page-hero-content p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .page-hero {
        min-height: 40vh;
    }

    .page-hero-content h1 {
        font-size: 2.5rem;
    }

    .page-hero-content p {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .page-hero {
        min-height: 30vh;
    }

    .page-hero-content h1 {
        font-size: 2rem;
    }

    .page-hero-content p {
        font-size: 1rem;
    }
}

/* Animated gradient background */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Glassmorphism overlay */
.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 1;
}

/* Floating particles background */
.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    animation: floatingParticles 20s ease-in-out infinite;
    z-index: 1;
}

@keyframes floatingParticles {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(10px) rotate(240deg); }
}

.hero-slider {
    width: 100%;
    position: relative;
    z-index: 2;
}

.hero-slide {
    display: none;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    gap: 6rem;
    padding: 6rem 0;
    position: relative;
    width: 100%;
    min-height: 70vh;
}

.hero-slide.active {
    display: grid;
    animation: slideIn 1s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content {
    color: white;
    z-index: 3;
    position: relative;
}

/* Modern typography with enhanced styling */
.hero-content h1 {
    font-size: 4.5rem;
    margin-bottom: 1.5rem;
    font-weight: 800;
    line-height: 1.1;
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    animation: textGlow 3s ease-in-out infinite alternate;
}

@keyframes textGlow {
    from { filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.3)); }
    to { filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.6)); }
}

.hero-content p {
    font-size: 1.4rem;
    margin-bottom: 3rem;
    opacity: 0.95;
    font-weight: 400;
    line-height: 1.6;
    max-width: 500px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Enhanced hero image with modern effects */
.hero-image {
    position: relative;
    z-index: 2;
}

.hero-image img {
    width: 100%;
    height: 600px;
    max-height: 70vh;
    object-fit: cover;
    border-radius: 30px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    transition: all 0.5s ease;
    position: relative;
}

.hero-image::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0.1) 100%);
    border-radius: 35px;
    z-index: -1;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.hero-image:hover img {
    transform: translateY(-10px) scale(1.02);
    box-shadow:
        0 35px 70px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2);
}

/* Modern Hero Elements */
.hero-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    padding: 8px 20px;
    margin-bottom: 2rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: white;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-cta-group {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.cta-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.cta-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

/* Hero Stats */
.hero-stats {
    display: flex;
    gap: 3rem;
    animation: fadeInUp 1s ease-out 0.8s both;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 800;
    color: white;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* Image Container Enhancements */
.image-container {
    position: relative;
}

.image-overlay {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 3;
}

.floating-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    animation: floatingCard 3s ease-in-out infinite;
}

@keyframes floatingCard {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.card-text {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.9rem;
}

.card-icon {
    font-size: 1.2rem;
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    z-index: 3;
    animation: fadeInUp 1s ease-out 1s both;
}

.scroll-arrow {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.scroll-indicator span {
    font-size: 0.8rem;
    font-weight: 500;
}

/* Fade in animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Performance Optimizations */
.hero,
.hero-slide,
.hero-content,
.hero-image {
    will-change: transform;
}

.hero-image img {
    will-change: transform, box-shadow;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .hero,
    .hero::before,
    .hero::after,
    .hero-content h1,
    .floating-card,
    .scroll-arrow,
    .hero-badge,
    .hero-cta-group,
    .hero-stats {
        animation: none !important;
        transition: none !important;
    }

    .cta-btn:hover,
    .hero-image:hover img {
        transform: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .hero-content h1 {
        -webkit-text-fill-color: white;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    }

    .hero-badge,
    .floating-card {
        border: 2px solid white;
    }

    .cta-btn {
        border: 2px solid white;
    }
}

/* Focus styles for accessibility */
.cta-btn:focus-visible {
    outline: 3px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
}

/* Preload critical animations */
.hero-slide.active .hero-content > * {
    animation-fill-mode: both;
}

/* Categories Section */
.categories {
    padding: 5rem 0;
    background-color: var(--section-bg);
    width: 100%;
    transition: background-color 0.3s ease;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    width: 100%;
}

.category-card {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
    height: 300px;
    min-height: 250px;
    width: 100%;
}

.category-card:hover {
    transform: translateY(-10px);
}

.category-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.category-card:hover img {
    transform: scale(1.1);
}

.category-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 2rem;
    text-align: center;
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.category-card:hover .category-overlay {
    transform: translateY(-10px);
}

.category-overlay h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.category-link {
    display: inline-block;
    padding: 0.8rem 2rem;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.category-link:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.category-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: 0.5s;
}

.category-link:hover::before {
    left: 100%;
}

/* Products Grid */
.featured-products, .trending {
    padding: 5rem 0;
    width: 100%;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
    width: 100%;
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 300px;
    width: 100%;
    min-height: 250px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .product-actions {
    opacity: 1;
}

.action-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: white;
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.product-info {
    padding: 1.5rem;
}

.product-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 1rem;
}

.current-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.original-price {
    font-size: 1rem;
    color: #999;
    text-decoration: line-through;
}

.discount-badge {
    background: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 1rem;
}

.stars {
    color: #ffc107;
}

.rating-count {
    color: #666;
    font-size: 0.9rem;
}

/* Enhanced product card actions */
.product-card-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 1rem;
}

.btn-outline {
    background: transparent;
    border: 2px solid #ddd;
    color: #666;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    border-color: #4B0082;
    color: #4B0082;
    background: rgba(75, 0, 130, 0.05);
}

.btn-outline.favorited {
    background: #4B0082;
    border-color: #4B0082;
    color: white;
}

.btn-outline.favorited:hover {
    background: #3a0066;
    border-color: #3a0066;
}

/* Enhanced product actions in image overlay */
.product-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .product-actions {
    opacity: 1;
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.action-btn:hover {
    background: #4B0082;
    color: white;
    transform: scale(1.1);
}

/* Trending Tags */
.trending-tags {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.tag {
    background: #f8f9fa;
    color: #666;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tag:hover {
    background: var(--primary-color);
    color: white;
}

/* Newsletter Section */
.newsletter {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    padding: 4rem 0;
    color: white;
    width: 100%;
}

.newsletter-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.newsletter-content h2 {
    margin-bottom: 1rem;
}

.newsletter-content p {
    margin-bottom: 2rem;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    gap: 1rem;
    max-width: 500px;
    width: 100%;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
}

.newsletter-form button {
    padding: 12px 24px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.newsletter-form button:hover {
    background: #3a0066;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.hidden {
    display: none;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
