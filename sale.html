<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sale - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Remove outlines from all elements */
        *:focus,
        *:active,
        *:focus-visible {
            outline: none !important;
        }

        body {
            padding-top: 80px;
        }
        
        .sale-hero {
            background: linear-gradient(135deg, #4B0082, #6a1b9a);
            color: white;
            padding: 3rem 0;
            text-align: center;
        }
        
        .sale-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .sale-hero p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        
        .sale-stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .stat {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            display: block;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .filters-section {
            background: #f8f9fa;
            padding: 2rem 0;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .filters-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .filters-left {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .filter-group label {
            font-weight: 500;
            color: #333;
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            cursor: pointer;
        }
        
        .filter-select:focus {
            outline: none;
            border-color: #4B0082;
        }
        
        .view-toggle {
            display: flex;
            gap: 0.5rem;
        }
        
        .view-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .view-btn.active {
            background: #4B0082;
            color: white;
            border-color: #4B0082;
        }
        
        .results-info {
            padding: 1rem 0;
            color: #666;
            font-size: 0.9rem;
        }
        
        .sale-products {
            padding: 2rem 0;
        }
        
        .products-grid.list-view {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .product-card.list-view {
            display: flex;
            align-items: center;
            padding: 1rem;
            gap: 1rem;
        }
        
        .product-card.list-view .product-image {
            width: 150px;
            height: 150px;
            flex-shrink: 0;
        }
        
        .product-card.list-view .product-info {
            flex: 1;
            padding: 0;
        }
        
        .product-card.list-view .product-actions {
            position: static;
            opacity: 1;
            flex-direction: row;
            gap: 0.5rem;
        }
        
        .sale-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #e74c3c;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            z-index: 2;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 3rem;
        }
        
        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .pagination button:hover {
            background: #f8f9fa;
        }
        
        .pagination button.active {
            background: #4B0082;
            color: white;
            border-color: #4B0082;
        }
        
        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .loading-products {
            text-align: center;
            padding: 3rem 0;
            color: #666;
        }
        
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4B0082;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-left">
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html">
                        <svg width="90" height="40" viewBox="0 0 90 40" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#4B0082;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#D8BFD8;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <text x="5" y="28" font-family="Inter, sans-serif" font-size="24" font-weight="700" fill="url(#logoGradient)">VAITH</text>
                        </svg>
                    </a>
                </div>
            </div>
            
            <div class="nav-center">
                <ul class="nav-links" id="navLinks">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html">Products</a></li>
                    <li><a href="sale.html" class="active">Sale</a></li>
                </ul>
                <div class="search-box">
                    <input type="text" placeholder="Search for products..." id="searchInput">
                    <button class="search-btn"><i class="fas fa-search"></i></button>
                </div>
            </div>

            <div class="nav-right">
                <div class="nav-icons">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <a href="login.html" class="nav-icon">
                        <i class="fas fa-user"></i>
                    </a>
                    <button class="nav-icon" id="favoritesBtn">
                        <i class="fas fa-heart"></i>
                        <span class="badge" id="favoritesCount">0</span>
                    </button>
                    <button class="nav-icon" id="cartBtn">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="badge" id="cartCount">0</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="page-hero">
        <div class="page-hero-content">
            <h1>Special Offers & Deals</h1>
            <p>Discover amazing discounts on our latest collections</p>
        </div>
    </section>

    <!-- Filters Section -->
    <section class="filters-section">
        <div class="container">
            <div class="filters-container">
                <div class="filters-left">
                    <div class="filter-group">
                        <label for="categoryFilter">Category:</label>
                        <select id="categoryFilter" class="filter-select">
                            <option value="all">All Categories</option>
                            <option value="women">Women</option>
                            <option value="men">Men</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="sortFilter">Sort by:</label>
                        <select id="sortFilter" class="filter-select">
                            <option value="featured">Featured</option>
                            <option value="price-low">Price: Low to High</option>
                            <option value="price-high">Price: High to Low</option>
                            <option value="discount">Highest Discount</option>
                            <option value="newest">Newest</option>
                        </select>
                    </div>
                </div>
                
                <div class="view-toggle">
                    <button class="view-btn active" id="gridViewBtn">
                        <i class="fas fa-th"></i>
                    </button>
                    <button class="view-btn" id="listViewBtn">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="sale-products">
        <div class="container">
            <div class="results-info">
                <span id="resultsCount">Loading products...</span>
            </div>
            
            <div class="loading-products" id="loadingProducts">
                <div class="loading-spinner"></div>
                <p>Loading sale products...</p>
            </div>
            
            <div class="products-grid" id="saleProductsGrid">
                <!-- Products will be loaded dynamically -->
            </div>
            
            <div class="pagination" id="pagination">
                <!-- Pagination will be generated dynamically -->
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>VAITH</h3>
                    <p>Your destination for trendy and affordable fashion.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Customer Service</h4>
                    <ul>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">Size Guide</a></li>
                        <li><a href="#">Shipping Info</a></li>
                        <li><a href="#">Returns</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Careers</a></li>
                        <li><a href="#">Press</a></li>
                        <li><a href="#">Sustainability</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Legal</h4>
                    <ul>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">Cookie Policy</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 VAITH. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Cart Modal -->
    <div class="modal" id="cartModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Shopping Cart</h3>
                <button class="close-modal" id="closeCart">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="cartItems">
                <!-- Cart items will be loaded dynamically -->
            </div>
            <div class="cart-footer">
                <div class="cart-total">
                    <strong>Total: $<span id="cartTotal">0.00</span></strong>
                </div>
                <button class="checkout-btn">Checkout</button>
            </div>
        </div>
    </div>

    <!-- Favorites Modal -->
    <div class="modal" id="favoritesModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Your Favorites</h3>
                <button class="close-modal" id="closeFavorites">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="favoritesItems">
                <!-- Favorites will be loaded dynamically -->
            </div>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay"></div>

    <!-- Scripts -->
    <script src="js/theme-toggle.js"></script>
    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/favorites.js"></script>
    
    <script>
        // Sale page specific functionality
        let currentPage = 1;
        let currentView = 'grid';
        let currentFilters = {
            category: '',
            sort: 'featured'
        };
        
        // Sample sale products data
        const saleProducts = [
            {
                id: 1,
                title: "Summer Floral Dress",
                price: 29.99,
                originalPrice: 49.99,
                image: "https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400&h=500&fit=crop",
                rating: 4.5,
                reviews: 128,
                category: "women",
                onSale: true
            },
            {
                id: 2,
                title: "Classic White Shirt",
                price: 24.99,
                originalPrice: 34.99,
                image: "https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=400&h=500&fit=crop",
                rating: 4.2,
                reviews: 89,
                category: "men",
                onSale: true
            },
            {
                id: 4,
                title: "Casual Sneakers",
                price: 79.99,
                originalPrice: 99.99,
                image: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=500&fit=crop",
                rating: 4.4,
                reviews: 156,
                category: "shoes",
                onSale: true
            },
            {
                id: 6,
                title: "Leather Handbag",
                price: 89.99,
                originalPrice: 129.99,
                image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=500&fit=crop",
                rating: 4.8,
                reviews: 74,
                category: "accessories",
                onSale: true
            },
            // Add more sale products...
            {
                id: 7,
                title: "Striped T-Shirt",
                price: 15.99,
                originalPrice: 24.99,
                image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=500&fit=crop",
                rating: 4.3,
                reviews: 67,
                category: "women",
                onSale: true
            },
            {
                id: 8,
                title: "Denim Jeans",
                price: 39.99,
                originalPrice: 59.99,
                image: "https://images.unsplash.com/photo-1542272604-787c3835535d?w=400&h=500&fit=crop",
                rating: 4.6,
                reviews: 143,
                category: "men",
                onSale: true
            }
        ];
        
        document.addEventListener('DOMContentLoaded', function() {
            initializeSalePage();
        });
        
        function initializeSalePage() {
            // Initialize filters
            document.getElementById('categoryFilter').addEventListener('change', handleFilterChange);
            document.getElementById('sortFilter').addEventListener('change', handleFilterChange);

            // Initialize view toggle
            document.getElementById('gridViewBtn').addEventListener('click', () => setView('grid'));
            document.getElementById('listViewBtn').addEventListener('click', () => setView('list'));

            // Load initial products
            loadProducts();
        }
        
        function handleFilterChange() {
            currentFilters.category = document.getElementById('categoryFilter').value;
            currentFilters.sort = document.getElementById('sortFilter').value;
            currentPage = 1;
            loadProducts();
        }
        
        function setView(view) {
            currentView = view;
            
            // Update button states
            document.getElementById('gridViewBtn').classList.toggle('active', view === 'grid');
            document.getElementById('listViewBtn').classList.toggle('active', view === 'list');
            
            // Update grid class
            const grid = document.getElementById('saleProductsGrid');
            grid.classList.toggle('list-view', view === 'list');
            
            // Re-render products with new view
            renderProducts(getFilteredProducts());
        }
        
        function loadProducts() {
            const loadingElement = document.getElementById('loadingProducts');
            const gridElement = document.getElementById('saleProductsGrid');
            
            // Show loading
            loadingElement.style.display = 'block';
            gridElement.style.display = 'none';
            
            // Simulate API call
            setTimeout(() => {
                const filteredProducts = getFilteredProducts();
                renderProducts(filteredProducts);
                updateResultsInfo(filteredProducts.length);
                renderPagination(filteredProducts.length);
                
                // Hide loading
                loadingElement.style.display = 'none';
                gridElement.style.display = currentView === 'list' ? 'flex' : 'grid';
            }, 500);
        }
        
        function getFilteredProducts() {
            let filtered = [...saleProducts];

            // Filter by category
            if (currentFilters.category) {
                filtered = filtered.filter(product => product.category === currentFilters.category);
            }

            // Sort products
            switch (currentFilters.sort) {
                case 'price-low':
                    filtered.sort((a, b) => a.price - b.price);
                    break;
                case 'price-high':
                    filtered.sort((a, b) => b.price - a.price);
                    break;
                case 'discount':
                    filtered.sort((a, b) => {
                        const discountA = a.originalPrice ? (a.originalPrice - a.price) / a.originalPrice : 0;
                        const discountB = b.originalPrice ? (b.originalPrice - b.price) / b.originalPrice : 0;
                        return discountB - discountA;
                    });
                    break;
                case 'newest':
                    // In a real app, this would sort by date
                    filtered.reverse();
                    break;
                default:
                    // Featured - keep original order
                    break;
            }
            
            return filtered;
        }
        
        function renderProducts(products) {
            const container = document.getElementById('saleProductsGrid');
            container.innerHTML = '';
            
            const itemsPerPage = 12;
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageProducts = products.slice(startIndex, endIndex);
            
            pageProducts.forEach(product => {
                const productCard = createSaleProductCard(product);
                container.appendChild(productCard);
            });
        }
        
        function createSaleProductCard(product) {
            const card = document.createElement('div');
            card.className = `product-card ${currentView === 'list' ? 'list-view' : ''}`;
            card.setAttribute('data-product-id', product.id);

            const discount = product.originalPrice ? 
                Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100) : 0;

            card.innerHTML = `
                <div class="product-image">
                    <img src="${product.image}" alt="${product.title}" loading="lazy">
                    <div class="sale-badge">-${discount}%</div>
                    <div class="product-actions">
                        <button class="action-btn favorite-btn" data-product-id="${product.id}">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button class="action-btn quick-view-btn" data-product-id="${product.id}">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <div class="product-info">
                    <h3 class="product-title">${product.title}</h3>
                    <div class="product-price">
                        <span class="current-price">$${product.price}</span>
                        <span class="original-price">$${product.originalPrice}</span>
                    </div>
                    <div class="product-rating">
                        <div class="stars">
                            ${generateStars(product.rating)}
                        </div>
                        <span class="rating-count">(${product.reviews})</span>
                    </div>
                    <button class="btn btn-primary add-to-cart-btn" data-product-id="${product.id}">
                        Add to Cart
                    </button>
                </div>
            `;

            // Add event listeners
            const favoriteBtn = card.querySelector('.favorite-btn');
            const addToCartBtn = card.querySelector('.add-to-cart-btn');
            const quickViewBtn = card.querySelector('.quick-view-btn');

            favoriteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleFavorite(product.id);
            });

            addToCartBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                addToCart(product);
            });

            quickViewBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                openProductModal(product);
            });

            // Navigate to product page on card click
            card.addEventListener('click', () => {
                window.location.href = `product.html?id=${product.id}`;
            });

            return card;
        }
        
        function updateResultsInfo(totalResults) {
            const resultsElement = document.getElementById('resultsCount');
            resultsElement.textContent = `Showing ${totalResults} sale items`;
        }
        
        function renderPagination(totalItems) {
            const container = document.getElementById('pagination');
            const itemsPerPage = 12;
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            
            if (totalPages <= 1) {
                container.innerHTML = '';
                return;
            }
            
            let paginationHTML = '';
            
            // Previous button
            paginationHTML += `
                <button ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;
            
            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    paginationHTML += `
                        <button class="${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                            ${i}
                        </button>
                    `;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHTML += '<span>...</span>';
                }
            }
            
            // Next button
            paginationHTML += `
                <button ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;
            
            container.innerHTML = paginationHTML;
        }
        
        function changePage(page) {
            currentPage = page;
            loadProducts();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
        
        // Make changePage available globally
        window.changePage = changePage;
    </script>
</body>
</html>
