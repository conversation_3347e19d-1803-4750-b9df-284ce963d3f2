// Simple Theme Toggle Implementation
console.log('Theme toggle script loaded');

// Initialize theme immediately
(function() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    console.log('Initializing theme:', savedTheme);
    
    if (savedTheme === 'dark') {
        document.documentElement.setAttribute('data-theme', 'dark');
        document.body.classList.add('dark-theme');
    }
})();

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM ready, setting up theme toggle');
    
    const themeToggle = document.getElementById('themeToggle');
    const themeIcon = document.getElementById('themeIcon');
    
    console.log('Found elements:', { themeToggle, themeIcon });
    
    if (!themeToggle || !themeIcon) {
        console.error('Theme toggle elements not found!');
        return;
    }
    
    // Set initial icon based on current theme
    const currentTheme = localStorage.getItem('theme') || 'light';
    updateIcon(currentTheme);
    
    // Add click handler
    themeToggle.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        console.log('Theme toggle clicked!');
        
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        console.log('Switching from', currentTheme, 'to', newTheme);
        
        // Apply new theme
        applyTheme(newTheme);
        
        // Save to localStorage
        localStorage.setItem('theme', newTheme);
        
        // Update icon
        updateIcon(newTheme);
        
        // Visual feedback
        themeToggle.style.transform = 'scale(0.9)';
        setTimeout(() => {
            themeToggle.style.transform = '';
        }, 150);
        
        console.log('Theme switched to:', newTheme);
    });
    
    console.log('Theme toggle setup complete');
});

function applyTheme(theme) {
    console.log('Applying theme:', theme);
    
    // Set data attribute
    document.documentElement.setAttribute('data-theme', theme);
    
    // Add/remove body class
    if (theme === 'dark') {
        document.body.classList.add('dark-theme');
    } else {
        document.body.classList.remove('dark-theme');
    }
    
    console.log('Theme applied. Data-theme:', document.documentElement.getAttribute('data-theme'));
    console.log('Body classes:', document.body.className);
}

function updateIcon(theme) {
    const themeIcon = document.getElementById('themeIcon');
    const themeToggle = document.getElementById('themeToggle');
    
    if (!themeIcon || !themeToggle) return;
    
    if (theme === 'dark') {
        themeIcon.className = 'fas fa-sun';
        themeToggle.setAttribute('title', 'Switch to light mode');
        themeToggle.setAttribute('aria-label', 'Switch to light mode');
    } else {
        themeIcon.className = 'fas fa-moon';
        themeToggle.setAttribute('title', 'Switch to dark mode');
        themeToggle.setAttribute('aria-label', 'Switch to dark mode');
    }
    
    console.log('Icon updated for theme:', theme);
}
